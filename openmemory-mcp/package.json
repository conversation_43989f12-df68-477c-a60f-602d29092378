{"name": "openmemory-mcp", "version": "1.0.0", "description": "OpenMemory MCP Service - Private, portable, and open-source memory for LLMs", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build"}, "keywords": ["mcp", "memory", "ai", "llm", "privacy", "local", "open-source"], "author": "OpenMemory Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "sqlite3": "^5.1.7", "better-sqlite3": "^9.2.2", "crypto-js": "^4.2.0", "uuid": "^9.0.1", "zod": "^3.22.4", "dotenv": "^16.3.1", "winston": "^3.11.0", "node-cron": "^3.0.3", "similarity": "^1.2.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@types/crypto-js": "^4.2.1", "@types/better-sqlite3": "^7.6.8", "@types/jest": "^29.5.8", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/openmemory/openmemory-mcp.git"}, "bugs": {"url": "https://github.com/openmemory/openmemory-mcp/issues"}, "homepage": "https://github.com/openmemory/openmemory-mcp#readme"}